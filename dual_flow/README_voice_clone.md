# 火山引擎双流式TTS声音复刻功能

本项目实现了火山引擎的双流式TTS声音复刻功能，支持自定义声音模型的训练和使用。

## 功能特性

### 🎯 核心功能
- **声音复刻模型训练**: 上传音频样本，训练个性化声音模型
- **双流式TTS合成**: 支持流式文本输入和实时音频输出
- **复刻发音人合成**: 使用训练好的复刻模型进行语音合成
- **混音功能**: 复刻发音人与预置发音人混合，创造独特音色
- **实时播放**: 边合成边播放，提供流畅的用户体验

### 🔧 技术特点
- 基于WebSocket的双向流式通信
- 支持多种文本输入方式（单次、流式、交互式、文件）
- 异步处理，高效并发
- 完整的错误处理和状态监控

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install asyncio websockets aiofiles pyaudio requests

# 您的火山引擎API凭证（已配置）
APP_ID = "1181316113"
TOKEN = "XliULxhgl1UU0zUkMuuTjzcJkOyryWLA"
CLONE_SPEAKER_ID = "S_Faev7OGv1"
```

### 2. 声音复刻模型训练

```bash
# 训练新的声音复刻模型
python voice_clone_tts_demo.py \
    --mode train \
    --audio-file /path/to/your/audio.wav \
    --speaker-id "my_custom_speaker_001" \
    --app-id "your_app_id" \
    --token "your_token"
```

**音频要求：**
- 格式：WAV, MP3, M4A等常见格式
- 时长：建议10-60秒
- 质量：清晰、无噪音、单人发音
- 内容：建议包含丰富的语音特征

### 3. 查询训练状态

```bash
# 查询模型训练状态
python voice_clone_tts_demo.py \
    --mode status \
    --speaker-id "my_custom_speaker_001"
```

### 4. 使用复刻发音人合成语音

#### 基础合成
```bash
# 使用复刻发音人合成语音
python voice_clone_tts_demo.py \
    --mode synthesize \
    --speaker-id "my_custom_speaker_001" \
    --text "这是使用复刻发音人合成的语音" \
    --output "output.wav"
```

#### 流式合成
```bash
# 启用流式文本输入
python voice_clone_tts_demo.py \
    --mode synthesize \
    --speaker-id "my_custom_speaker_001" \
    --stream \
    --play
```

#### 混音合成
```bash
# 复刻发音人与预置发音人混音
python voice_clone_tts_demo.py \
    --mode synthesize \
    --speaker-id "my_custom_speaker_001" \
    --text "混音效果演示" \
    --mix \
    --preset-speaker "zh_female_shuangkuaisisi_moon_bigtts" \
    --mix-factor 0.3
```

#### 交互式合成
```bash
# 交互式输入文本
python voice_clone_tts_demo.py \
    --mode synthesize \
    --speaker-id "my_custom_speaker_001" \
    --interactive \
    --play
```

#### 从文件读取文本
```bash
# 从文件读取文本进行合成
python voice_clone_tts_demo.py \
    --mode synthesize \
    --speaker-id "my_custom_speaker_001" \
    --input-file "text.txt" \
    --play
```

## API 使用示例

### Python代码示例

```python
import asyncio
from voice_clone_tts_demo import VoiceCloneTTSDemo

async def example_usage():
    # 初始化
    demo = VoiceCloneTTSDemo("your_app_id", "your_token")
    
    # 1. 训练声音复刻模型
    success = await demo.train_voice_clone(
        audio_file="sample.wav",
        speaker_id="my_speaker_001"
    )
    
    if success:
        # 2. 使用复刻发音人合成语音
        await demo.synthesize_with_clone(
            speaker_id="my_speaker_001",
            text="这是复刻语音测试",
            output_path="output.wav",
            stream_audio=True
        )
        
        # 3. 混音合成
        await demo.synthesize_with_clone(
            speaker_id="my_speaker_001",
            text="这是混音效果测试",
            mix_with_preset=True,
            preset_speaker="zh_male_bvlazysheep",
            mix_factor=0.3
        )

# 运行示例
asyncio.run(example_usage())
```

### 流式文本生成器示例

```python
async def custom_text_generator():
    """自定义文本生成器"""
    texts = [
        "第一段文本内容",
        "第二段文本内容", 
        "第三段文本内容"
    ]
    for text in texts:
        yield text
        await asyncio.sleep(1)  # 控制发送间隔

# 使用自定义生成器
await demo.synthesize_with_clone(
    speaker_id="my_speaker_001",
    text_generator=custom_text_generator,
    stream_audio=True
)
```

## 参数说明

### 训练参数
- `--audio-file`: 训练音频文件路径
- `--speaker-id`: 自定义发音人ID（唯一标识）
- `--language`: 语言类型（0: 中文, 1: 英文）
- `--model-type`: 模型类型（1: 标准模型）

### 合成参数
- `--text`: 要合成的文本内容
- `--output`: 输出音频文件路径
- `--play`: 是否实时播放音频
- `--stream`: 启用流式文本输入
- `--interactive`: 交互式文本输入
- `--input-file`: 从文件读取文本

### 混音参数
- `--mix`: 启用混音功能
- `--preset-speaker`: 预置发音人ID
- `--mix-factor`: 混音比例（0.0-1.0）

## 注意事项

### 🚨 重要提醒
1. **API配额**: 声音复刻功能可能有使用配额限制
2. **训练时间**: 模型训练通常需要几分钟到几十分钟
3. **音频质量**: 训练音频的质量直接影响复刻效果
4. **合规使用**: 请确保有合法权限使用相关音频内容

### 🔧 故障排除
1. **训练失败**: 检查音频文件格式和质量
2. **合成失败**: 确认speaker_id已训练完成
3. **连接错误**: 检查网络连接和API凭证
4. **音频播放问题**: 确认系统音频设备正常

### 📈 性能优化
1. **并发控制**: 避免同时进行多个训练任务
2. **缓存管理**: 合理管理生成的音频文件
3. **网络优化**: 在稳定网络环境下使用

## 更多功能

项目还支持更多高级功能，详见源码中的其他示例：
- `TTSWebSocketDemo.py`: 基础双流式TTS功能
- `uploadAndStatus.py`: 声音复刻管理功能
- `tts_websocket_demo.py`: 传统WebSocket TTS

## 技术支持

如有问题，请参考：
1. 火山引擎官方文档
2. 项目源码注释
3. 错误日志输出
