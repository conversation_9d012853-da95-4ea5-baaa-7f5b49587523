import asyncio
import json
import uuid
import io
import argparse
import sys
import base64
import os
import requests
import time

import aiofiles
import websocket
import websockets
import pyaudio
import wave
from websockets.asyncio.client import ClientConnection

PROTOCOL_VERSION = 0b0001
DEFAULT_HEADER_SIZE = 0b0001

# Message Type:
FULL_CLIENT_REQUEST = 0b0001
AUDIO_ONLY_RESPONSE = 0b1011
FULL_SERVER_RESPONSE = 0b1001
ERROR_INFORMATION = 0b1111

# Message Type Specific Flags
MsgTypeFlagNoSeq = 0b0000  # Non-terminal packet with no sequence
MsgTypeFlagPositiveSeq = 0b1  # Non-terminal packet with sequence > 0
MsgTypeFlagLastNoSeq = 0b10  # last packet with no sequence
MsgTypeFlagNegativeSeq = 0b11  # Payload contains event number (int32)
MsgTypeFlagWithEvent = 0b100
# Message Serialization
NO_SERIALIZATION = 0b0000
JSON = 0b0001
# Message Compression
COMPRESSION_NO = 0b0000
COMPRESSION_GZIP = 0b0001

EVENT_NONE = 0
EVENT_Start_Connection = 1

EVENT_FinishConnection = 2

EVENT_ConnectionStarted = 50  # 成功建连

EVENT_ConnectionFailed = 51  # 建连失败（可能是无法通过权限认证）

EVENT_ConnectionFinished = 52  # 连接结束

# 上行Session事件
EVENT_StartSession = 100

EVENT_FinishSession = 102
# 下行Session事件
EVENT_SessionStarted = 150
EVENT_SessionFinished = 152

EVENT_SessionFailed = 153

# 上行通用事件
EVENT_TaskRequest = 200

# 下行TTS事件
EVENT_TTSSentenceStart = 350

EVENT_TTSSentenceEnd = 351

EVENT_TTSResponse = 352

# 音频播放相关参数
CHUNK = 1024
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 24000


class Header:
    def __init__(self,
                 protocol_version=PROTOCOL_VERSION,
                 header_size=DEFAULT_HEADER_SIZE,
                 message_type: int = 0,
                 message_type_specific_flags: int = 0,
                 serial_method: int = NO_SERIALIZATION,
                 compression_type: int = COMPRESSION_NO,
                 reserved_data=0):
        self.header_size = header_size
        self.protocol_version = protocol_version
        self.message_type = message_type
        self.message_type_specific_flags = message_type_specific_flags
        self.serial_method = serial_method
        self.compression_type = compression_type
        self.reserved_data = reserved_data

    def as_bytes(self) -> bytes:
        return bytes([
            (self.protocol_version << 4) | self.header_size,
            (self.message_type << 4) | self.message_type_specific_flags,
            (self.serial_method << 4) | self.compression_type,
            self.reserved_data
        ])


class Optional:
    def __init__(self, event: int = EVENT_NONE, sessionId: str = None, sequence: int = None):
        self.event = event
        self.sessionId = sessionId
        self.errorCode: int = 0
        self.connectionId: str | None = None
        self.response_meta_json: str | None = None
        self.sequence = sequence

    # 转成 byte 序列
    def as_bytes(self) -> bytes:
        option_bytes = bytearray()
        if self.event != EVENT_NONE:
            option_bytes.extend(self.event.to_bytes(4, "big", signed=True))
        if self.sessionId is not None:
            session_id_bytes = str.encode(self.sessionId)
            size = len(session_id_bytes).to_bytes(4, "big", signed=True)
            option_bytes.extend(size)
            option_bytes.extend(session_id_bytes)
        if self.sequence is not None:
            option_bytes.extend(self.sequence.to_bytes(4, "big", signed=True))
        return option_bytes


class Response:
    def __init__(self, header: Header, optional: Optional):
        self.optional = optional
        self.header = header
        self.payload: bytes | None = None
        self.payload_json: str | None = None

    def __str__(self):
        return super().__str__()


class VoiceCloneManager:
    """声音复刻管理类"""

    def __init__(self, appid: str, token: str, host: str = "https://openspeech.bytedance.com"):
        self.appid = appid
        self.token = token
        self.host = host

    def encode_audio_file(self, file_path: str):
        """编码音频文件为base64"""
        with open(file_path, 'rb') as audio_file:
            audio_data = audio_file.read()
            encoded_data = str(base64.b64encode(audio_data), "utf-8")
            audio_format = os.path.splitext(file_path)[1][1:]  # 获取文件扩展名作为音频格式
            return encoded_data, audio_format

    def upload_and_train(self, audio_path: str, speaker_id: str, language: int = 0, model_type: int = 1):
        """上传音频并训练复刻模型"""
        url = self.host + "/api/v1/mega_tts/audio/upload"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer;" + self.token,
            "Resource-Id": "volc.megatts.voiceclone",
        }

        encoded_data, audio_format = self.encode_audio_file(audio_path)
        audios = [{"audio_bytes": encoded_data, "audio_format": audio_format}]
        data = {
            "appid": self.appid,
            "speaker_id": speaker_id,
            "audios": audios,
            "source": 2,
            "language": language,
            "model_type": model_type
        }

        response = requests.post(url, json=data, headers=headers)
        print(f"训练请求状态码: {response.status_code}")

        if response.status_code != 200:
            raise Exception(f"训练请求错误: {response.text}")

        result = response.json()
        print(f"训练响应: {result}")
        return result

    def get_training_status(self, speaker_id: str):
        """获取训练状态"""
        url = self.host + "/api/v1/mega_tts/status"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer;" + self.token,
            "Resource-Id": "volc.megatts.voiceclone",
        }
        body = {"appid": self.appid, "speaker_id": speaker_id}
        response = requests.post(url, headers=headers, json=body)

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"状态查询失败: {response.text}")

    def wait_for_training_completion(self, speaker_id: str, max_wait_time: int = 300, check_interval: int = 10):
        """等待训练完成"""
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                status_result = self.get_training_status(speaker_id)
                print(f"训练状态: {status_result}")

                # 根据火山引擎API文档，检查状态字段
                if 'data' in status_result and 'status' in status_result['data']:
                    status = status_result['data']['status']
                    if status == 'success':
                        print(f"声音复刻训练完成: {speaker_id}")
                        return True
                    elif status == 'failed':
                        print(f"声音复刻训练失败: {speaker_id}")
                        return False

                print(f"训练中，等待 {check_interval} 秒后重新检查...")
                time.sleep(check_interval)

            except Exception as e:
                print(f"检查训练状态时出错: {e}")
                time.sleep(check_interval)

        print(f"训练超时，超过最大等待时间 {max_wait_time} 秒")
        return False

    def get_clone_speaker_list(self):
        """获取复刻发音人列表，返回正确的speaker ID"""
        url = self.host + "/api/v1/mega_tts/speaker/list"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer;" + self.token,
            "Resource-Id": "volc.megatts.voiceclone",
        }
        body = {"appid": self.appid}
        response = requests.post(url, headers=headers, json=body)

        if response.status_code == 200:
            result = response.json()
            print(f"复刻发音人列表: {result}")
            return result
        else:
            raise Exception(f"获取复刻发音人列表失败: {response.text}")

    def get_icl_speaker_id(self, s_speaker_id: str):
        """根据S_开头的speaker_id获取对应的icl_开头的speaker_id"""
        try:
            speaker_list = self.get_clone_speaker_list()
            if 'data' in speaker_list and 'speakers' in speaker_list['data']:
                for speaker in speaker_list['data']['speakers']:
                    if speaker.get('speaker_id') == s_speaker_id:
                        icl_id = speaker.get('icl_speaker_id')
                        if icl_id:
                            print(f"找到对应的ICL Speaker ID: {s_speaker_id} -> {icl_id}")
                            return icl_id
            print(f"未找到对应的ICL Speaker ID: {s_speaker_id}")
            return s_speaker_id  # 如果找不到，返回原ID
        except Exception as e:
            print(f"获取ICL Speaker ID时出错: {e}")
            return s_speaker_id  # 出错时返回原ID


class AudioPlayer:
    def __init__(self, format=FORMAT, channels=CHANNELS, rate=RATE, chunk=CHUNK):
        self.format = format
        self.channels = channels
        self.rate = rate
        self.chunk = chunk
        self.p = pyaudio.PyAudio()
        self.stream = None

    def start_stream(self):
        if self.stream is None:
            self.stream = self.p.open(
                format=self.format,
                channels=self.channels,
                rate=self.rate,
                output=True,
                frames_per_buffer=self.chunk
            )
            print("音频播放流已启动")

    def play_audio(self, audio_data):
        if self.stream is None:
            self.start_stream()

        try:
            # 直接播放接收到的PCM音频数据
            self.stream.write(audio_data)
        except Exception as e:
            print(f"播放音频时出错: {e}")

    def stop_stream(self):
        if self.stream is not None:
            self.stream.stop_stream()
            self.stream.close()
            self.stream = None
            print("音频播放流已停止")

    def close(self):
        self.stop_stream()
        self.p.terminate()
        print("音频播放器已关闭")


# 发送事件
async def send_event(ws: websocket, header: bytes, optional: bytes | None = None,
                     payload: bytes = None):
    full_client_request = bytearray(header)
    if optional is not None:
        full_client_request.extend(optional)
    if payload is not None:
        payload_size = len(payload).to_bytes(4, 'big', signed=True)
        full_client_request.extend(payload_size)
        full_client_request.extend(payload)
    await ws.send(full_client_request)


# 读取 res 数组某段 字符串内容
def read_res_content(res: bytes, offset: int):
    content_size = int.from_bytes(res[offset: offset + 4])
    offset += 4
    content = str(res[offset: offset + content_size], encoding='utf8')
    offset += content_size
    return content, offset


# 读取 payload
def read_res_payload(res: bytes, offset: int):
    payload_size = int.from_bytes(res[offset: offset + 4])
    offset += 4
    payload = res[offset: offset + payload_size]
    offset += payload_size
    return payload, offset


# 解析响应结果
def parser_response(res) -> Response:
    if isinstance(res, str):
        raise RuntimeError(res)
    response = Response(Header(), Optional())
    # 解析结果
    # header
    header = response.header
    num = 0b00001111
    header.protocol_version = res[0] >> 4 & num
    header.header_size = res[0] & 0x0f
    header.message_type = (res[1] >> 4) & num
    header.message_type_specific_flags = res[1] & 0x0f
    header.serialization_method = res[2] >> num
    header.message_compression = res[2] & 0x0f
    header.reserved = res[3]
    #
    offset = 4
    optional = response.optional
    if header.message_type == FULL_SERVER_RESPONSE or AUDIO_ONLY_RESPONSE:
        # read event
        if header.message_type_specific_flags == MsgTypeFlagWithEvent:
            optional.event = int.from_bytes(res[offset:8])
            offset += 4
            if optional.event == EVENT_NONE:
                return response
            # read connectionId
            elif optional.event == EVENT_ConnectionStarted:
                optional.connectionId, offset = read_res_content(res, offset)
            elif optional.event == EVENT_ConnectionFailed:
                optional.response_meta_json, offset = read_res_content(res, offset)
            elif (optional.event == EVENT_SessionStarted
                  or optional.event == EVENT_SessionFailed
                  or optional.event == EVENT_SessionFinished):
                optional.sessionId, offset = read_res_content(res, offset)
                optional.response_meta_json, offset = read_res_content(res, offset)
            elif optional.event == EVENT_TTSResponse:
                optional.sessionId, offset = read_res_content(res, offset)
                response.payload, offset = read_res_payload(res, offset)
            elif optional.event == EVENT_TTSSentenceEnd or optional.event == EVENT_TTSSentenceStart:
                optional.sessionId, offset = read_res_content(res, offset)
                response.payload_json, offset = read_res_content(res, offset)

    elif header.message_type == ERROR_INFORMATION:
        optional.errorCode = int.from_bytes(res[offset:offset + 4], "big", signed=True)
        offset += 4
        response.payload, offset = read_res_payload(res, offset)
        # 解析错误信息
        if response.payload:
            try:
                error_msg = response.payload.decode('utf-8')
                response.payload_json = error_msg
                print(f"❌ 服务器错误信息: {error_msg}")
            except:
                print(f"❌ 服务器错误码: {optional.errorCode}")
    return response


async def run_demo(appId: str, token: str, speaker: str, text: str, output_path: str, text_generator=None,
                   stream_audio=False, use_clone_speaker=False, clone_speaker_id=None,
                   mix_speakers=None, post_process=None):
    # 根据是否使用复刻发音人选择不同的Resource ID
    if use_clone_speaker:
        resource_id = 'volc.megatts.default'  # 复刻发音人使用这个Resource ID
    else:
        resource_id = 'volc.service_type.10029'  # 大模型语音合成使用这个Resource ID

    ws_header = {
        "X-Api-App-Key": appId,
        "X-Api-Access-Key": token,
        "X-Api-Resource-Id": resource_id,
        "X-Api-Connect-Id": str(uuid.uuid4()),
    }
    url = 'wss://openspeech.bytedance.com/api/v3/tts/bidirection'

    print(f"🔗 连接信息:")
    print(f"  URL: {url}")
    print(f"  App ID: {appId}")
    print(f"  Token: {token[:10]}...{token[-10:]}")
    if use_clone_speaker:
        print(f"  复刻发音人ID: {clone_speaker_id}")
    else:
        print(f"  预置发音人: {speaker}")

    # 初始化音频播放器（如果需要流式播放）
    audio_player = None
    if stream_audio:
        audio_player = AudioPlayer()

    try:
        async with websockets.connect(url, extra_headers=ws_header, max_size=1000000000, ping_interval=30, ping_timeout=10) as ws:
            print("📡 发送start_connection请求...")
            await start_connection(ws)
            res = parser_response(await ws.recv())
            print_response(res, 'start_connection response:')
            if res.optional.event != EVENT_ConnectionStarted:
                error_msg = f"连接建立失败，事件码: {res.optional.event}"
                if res.payload_json:
                    error_msg += f", 错误信息: {res.payload_json}"
                raise RuntimeError(error_msg)

            session_id = uuid.uuid4().__str__().replace('-', '')
            print(f"📋 发送start_session请求 (session_id: {session_id})...")
            await start_session(ws, speaker, session_id, use_clone_speaker, clone_speaker_id, mix_speakers, post_process)
            res = parser_response(await ws.recv())
            print_response(res, 'start_session response:')
            if res.optional.event != EVENT_SessionStarted:
                error_msg = f"会话建立失败，事件码: {res.optional.event}"
                if res.payload_json:
                    error_msg += f", 错误信息: {res.payload_json}"
                if res.optional.event == EVENT_SessionFailed:
                    error_msg += "\n可能的原因："
                    error_msg += "\n1. 复刻发音人ID不正确或未训练完成"
                    error_msg += "\n2. Resource ID设置错误"
                    error_msg += "\n3. API配额不足"
                    error_msg += "\n4. 参数格式错误"
                raise RuntimeError(error_msg)

            # 创建音频输出文件
            async with aiofiles.open(output_path, mode="wb") as output_file:
                # 如果提供了文本生成器，使用流式输入
                if text_generator:
                    # 启动一个任务来处理接收音频数据
                    audio_task = asyncio.create_task(receive_audio_stream(ws, output_file, audio_player))

                    # 流式发送文本
                    print("🌊 开始流式发送文本...")
                    async for text_chunk in text_generator():  # 调用text_generator函数
                        if text_chunk:  # 确保文本块不为空
                            print(f"📝 发送文本块: {text_chunk}")
                            await send_text(ws, speaker, text_chunk, session_id, use_clone_speaker, clone_speaker_id, mix_speakers, post_process)
                            print(f"✅ 文本块发送完成")

                    # 完成会话
                    await finish_session(ws, session_id)

                    # 等待音频处理完成
                    await audio_task
                else:
                    # 使用原有的一次性发送文本方式
                    print(f"📝 发送文本: {text[:50]}{'...' if len(text) > 50 else ''}")
                    await send_text(ws, speaker, text, session_id, use_clone_speaker, clone_speaker_id, mix_speakers, post_process)
                    print("🔚 发送finish_session请求...")
                    await finish_session(ws, session_id)

                    # 接收音频数据
                    while True:
                        res = parser_response(await ws.recv())
                        print_response(res, 'send_text response:')
                        if res.optional.event == EVENT_TTSResponse and res.header.message_type == AUDIO_ONLY_RESPONSE:
                            await output_file.write(res.payload)
                            # 如果启用了流式播放，播放音频数据
                            if stream_audio and audio_player:
                                audio_player.play_audio(res.payload)
                        elif res.optional.event in [EVENT_TTSSentenceStart, EVENT_TTSSentenceEnd]:
                            continue
                        else:
                            break

            await finish_connection(ws)
            res = parser_response(await ws.recv())
            print_response(res, 'finish_connection response:')
            print('===> 退出程序')

            # 关闭音频播放器
            if stream_audio and audio_player:
                audio_player.close()

    except websockets.exceptions.ConnectionClosedError as e:
        print(f"❌ WebSocket连接被关闭: {e}")
        raise
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"❌ WebSocket连接状态码错误: {e}")
        print("可能的原因:")
        print("1. API密钥或应用ID不正确")
        print("2. 账户权限不足")
        print("3. 服务暂时不可用")
        raise
    except Exception as e:
        print(f"❌ 连接或处理过程中出错: {e}")
        if "unexpected error" in str(e):
            print("这通常表示服务器返回了错误响应")
            print("请检查:")
            print("1. 复刻发音人ID是否正确且已训练完成")
            print("2. API配额是否充足")
            print("3. 网络连接是否稳定")
        raise
    finally:
        # 确保音频播放器被正确关闭
        if stream_audio and audio_player:
            try:
                audio_player.close()
            except:
                pass


async def receive_audio_stream(ws, output_file, audio_player=None):
    """接收并处理流式音频数据"""
    while True:
        try:
            res = parser_response(await ws.recv())
            if res.optional.event == EVENT_TTSResponse and res.header.message_type == AUDIO_ONLY_RESPONSE:
                # 写入音频数据到文件
                if res.payload:
                    await output_file.write(res.payload)
                    # 如果启用了流式播放，播放音频数据
                    if audio_player:
                        audio_player.play_audio(res.payload)
                    print(f"接收到音频数据: {len(res.payload)} 字节")
            elif res.optional.event in [EVENT_TTSSentenceStart, EVENT_TTSSentenceEnd]:
                # 处理句子开始/结束事件
                print(f"句子事件: {res.optional.event}, 数据: {res.payload_json}")
                continue
            elif res.optional.event == EVENT_SessionFinished:
                # 会话结束
                print("会话已结束")
                break
            else:
                print(f"收到其他事件: {res.optional.event}")
                if res.optional.event == EVENT_SessionFailed:
                    print(f"会话失败: {res.optional.response_meta_json}")
                    break
        except Exception as e:
            print(f"接收音频数据时出错: {e}")
            break


def print_response(res, tag: str):
    print(f'===>{tag} header:{res.header.__dict__}')
    print(f'===>{tag} optional:{res.optional.__dict__}')
    print(f'===>{tag} payload len:{0 if res.payload is None else len(res.payload)}')
    print(f'===>{tag} payload_json:{res.payload_json}')


def get_payload_bytes(uid='1234', event=EVENT_NONE, text='', speaker='', audio_format='pcm',
                      audio_sample_rate=24000, use_clone_speaker=False, clone_speaker_id=None,
                      mix_speakers=None, post_process=None):
    """
    生成请求payload

    Args:
        uid: 用户ID
        event: 事件类型
        text: 要合成的文本
        speaker: 发音人ID（预置发音人）
        audio_format: 音频格式
        audio_sample_rate: 采样率
        use_clone_speaker: 是否使用复刻发音人
        clone_speaker_id: 复刻发音人ID
        mix_speakers: 混音发音人列表
        post_process: 后处理参数
    """

    payload = {
        "user": {"uid": uid},
        "event": event,
        "namespace": "BidirectionalTTS",
        "req_params": {
            "text": text,
            "audio_params": {
                "format": audio_format,
                "sample_rate": audio_sample_rate,
            }
        }
    }

    # 根据是否使用复刻发音人来设置speaker参数和后处理参数
    if use_clone_speaker and clone_speaker_id:
        # 使用复刻发音人
        payload["req_params"]["speaker"] = clone_speaker_id
        print(f"使用复刻发音人: {clone_speaker_id}")

        # 复刻发音人需要在additions中设置post_process
        if post_process:
            payload["req_params"]["additions"] = json.dumps({"post_process": post_process})
        else:
            payload["req_params"]["additions"] = json.dumps({"post_process": {"pitch": 12}})
    elif speaker:
        # 使用预置发音人
        payload["req_params"]["speaker"] = speaker
        print(f"使用预置发音人: {speaker}")

        # 预置发音人的后处理参数设置在audio_params中
        if post_process:
            payload["req_params"]["audio_params"]["post_process"] = post_process
        else:
            payload["req_params"]["audio_params"]["post_process"] = {"pitch": 12}

    # 添加混音发音人配置
    if mix_speakers and isinstance(mix_speakers, list):
        payload["mix_speaker"] = {"speakers": mix_speakers}
        print(f"使用混音发音人: {mix_speakers}")

    return str.encode(json.dumps(payload))


async def start_connection(websocket):
    header = Header(message_type=FULL_CLIENT_REQUEST, message_type_specific_flags=MsgTypeFlagWithEvent).as_bytes()
    optional = Optional(event=EVENT_Start_Connection).as_bytes()
    payload = str.encode("{}")
    return await send_event(websocket, header, optional, payload)


async def start_session(websocket, speaker, session_id, use_clone_speaker=False,
                       clone_speaker_id=None, mix_speakers=None, post_process=None):
    header = Header(message_type=FULL_CLIENT_REQUEST,
                    message_type_specific_flags=MsgTypeFlagWithEvent,
                    serial_method=JSON
                    ).as_bytes()
    optional = Optional(event=EVENT_StartSession, sessionId=session_id).as_bytes()
    payload = get_payload_bytes(
        event=EVENT_StartSession,
        speaker=speaker,
        use_clone_speaker=use_clone_speaker,
        clone_speaker_id=clone_speaker_id,
        mix_speakers=mix_speakers,
        post_process=post_process
    )
    return await send_event(websocket, header, optional, payload)


async def send_text(ws: ClientConnection, speaker: str, text: str, session_id,
                   use_clone_speaker=False, clone_speaker_id=None, mix_speakers=None, post_process=None):
    header = Header(message_type=FULL_CLIENT_REQUEST,
                    message_type_specific_flags=MsgTypeFlagWithEvent,
                    serial_method=JSON).as_bytes()
    optional = Optional(event=EVENT_TaskRequest, sessionId=session_id).as_bytes()
    payload = get_payload_bytes(
        event=EVENT_TaskRequest,
        text=text,
        speaker=speaker,
        use_clone_speaker=use_clone_speaker,
        clone_speaker_id=clone_speaker_id,
        mix_speakers=mix_speakers,
        post_process=post_process
    )
    return await send_event(ws, header, optional, payload)


async def finish_session(ws, session_id):
    header = Header(message_type=FULL_CLIENT_REQUEST,
                    message_type_specific_flags=MsgTypeFlagWithEvent,
                    serial_method=JSON
                    ).as_bytes()
    optional = Optional(event=EVENT_FinishSession, sessionId=session_id).as_bytes()
    payload = str.encode('{}')
    return await send_event(ws, header, optional, payload)


async def finish_connection(ws):
    header = Header(message_type=FULL_CLIENT_REQUEST,
                    message_type_specific_flags=MsgTypeFlagWithEvent,
                    serial_method=JSON
                    ).as_bytes()
    optional = Optional(event=EVENT_FinishConnection).as_bytes()
    payload = str.encode('{}')
    return await send_event(ws, header, optional, payload)


# 示例文本生成器
async def example_text_generator():
    texts = [
        "这是第一段文本，",
        "这是第二段文本，",
        "这是第三段文本。"
    ]
    for text in texts:
        yield text
        await asyncio.sleep(1)  # 模拟文本生成延迟


# 从文件读取文本并流式生成
async def file_text_generator(file_path):
    try:
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()

        # 按句子分割
        sentences = []
        for sentence in content.replace('。', '。\n').replace('！', '！\n').replace('？', '？\n').split('\n'):
            if sentence.strip():
                sentences.append(sentence.strip())

        for sentence in sentences:
            yield sentence
            await asyncio.sleep(0.5)  # 每句之间稍微暂停
    except Exception as e:
        print(f"读取文件出错: {e}")
        yield "无法读取文件内容。"


# 交互式输入文本生成器
async def interactive_text_generator():
    print("请输入文本（输入空行结束）：")
    while True:
        try:
            line = input("> ")
            if not line:
                break
            yield line
            # await asyncio.sleep(0.1)
        except EOFError:
            break
        except KeyboardInterrupt:
            break


def parse_arguments():
    parser = argparse.ArgumentParser(description='火山引擎TTS WebSocket流式合成示例')

    # 必要参数
    parser.add_argument('--app-id', required=True, help='火山引擎应用ID')
    parser.add_argument('--token', required=True, help='火山引擎访问令牌')
    parser.add_argument('--speaker', required=True, help='发音人ID')

    # 可选参数
    parser.add_argument('--text', help='要合成的文本')
    parser.add_argument('--output', default='output.mp3', help='输出音频文件路径')
    parser.add_argument('--input-file', help='从文件读取文本')
    parser.add_argument('--stream', action='store_true', help='启用流式输入')
    parser.add_argument('--play', action='store_true', help='实时播放音频')
    parser.add_argument('--interactive', action='store_true', help='交互式输入文本')

    return parser.parse_args()


async def demo_voice_clone_usage():
    """演示声音复刻功能的使用"""
    appId = '1181316113'
    token = 'XliULxhgl1UU0zUkMuuTjzcJkOyryWLA'

    # 初始化声音复刻管理器
    clone_manager = VoiceCloneManager(appId, token)

    # # 示例1: 使用预置发音人（原有功能）
    # print("🎵 示例1: 使用预置发音人")
    # speaker = 'zh_female_shuangkuaisisi_moon_bigtts'
    # text = '这是使用预置发音人的语音合成示例。'
    # output_path = '/Users/<USER>/Downloads/preset_speaker.wav'
    #
    # await run_demo(
    #     appId=appId,
    #     token=token,
    #     speaker=speaker,
    #     text=text,
    #     output_path=output_path,
    #     stream_audio=True
    # )

    # 示例2: 使用复刻发音人（新功能）
    print("\n🎭 示例2: 使用复刻发音人")
    s_speaker_id = "S_Faev7OGv1"  # 您的S_开头的复刻发音人ID

    # 获取正确的ICL Speaker ID
    try:
        icl_speaker_id = clone_manager.get_icl_speaker_id(s_speaker_id)
        print(f"将使用ICL Speaker ID: {icl_speaker_id}")
    except Exception as e:
        print(f"获取ICL Speaker ID失败，使用原ID: {e}")
        icl_speaker_id = s_speaker_id

    text = '这是使用声音复刻技术生成的语音，听起来是不是很像原始声音？'
    output_path = '/Users/<USER>/Downloads/clone_speaker.wav'

    await run_demo(
        appId=appId,
        token=token,
        speaker="",  # 使用复刻发音人时可以为空
        text=text,
        output_path=output_path,
        stream_audio=True,
        use_clone_speaker=True,
        clone_speaker_id=icl_speaker_id
    )

    # # 示例3: 复刻发音人与预置发音人混音
    # print("\n🎶 示例3: 复刻发音人混音")
    # mix_speakers = [{
    #     "source_speaker": "zh_male_bvlazysheep",
    #     "mix_factor": 0.3
    # }]
    # text = '这是复刻发音人与预置发音人混音的效果，创造出独特的音色。'
    # output_path = '/Users/<USER>/Downloads/mix_speaker.wav'
    #
    # await run_demo(
    #     appId=appId,
    #     token=token,
    #     speaker="",
    #     text=text,
    #     output_path=output_path,
    #     stream_audio=True,
    #     use_clone_speaker=True,
    #     clone_speaker_id=clone_speaker_id,
    #     mix_speakers=mix_speakers
    # )
    #
    # # 示例4: 流式文本 + 复刻发音人
    # print("\n🌊 示例4: 流式文本 + 复刻发音人")
    #
    # async def stream_text_generator():
    #     texts = [
    #         "这是流式文本合成的演示，",
    #         "使用复刻发音人进行语音合成，",
    #         "文本会分段发送，",
    #         "音频实时生成和播放。"
    #     ]
    #     for text in texts:
    #         yield text
    #         await asyncio.sleep(0.5)
    #
    # output_path = '/Users/<USER>/Downloads/stream_clone.wav'
    #
    # await run_demo(
    #     appId=appId,
    #     token=token,
    #     speaker="",
    #     text="",
    #     output_path=output_path,
    #     text_generator=stream_text_generator,
    #     stream_audio=True,
    #     use_clone_speaker=True,
    #     clone_speaker_id=clone_speaker_id
    # )


if __name__ == "__main__":
    # print("🎤 火山引擎双流式TTS声音复刻演示")
    # print("=" * 50)
    # print("本演示将展示以下功能：")
    # print("1. 预置发音人语音合成")
    # print("2. 复刻发音人语音合成")
    # print("3. 复刻发音人混音合成")
    # print("4. 流式文本 + 复刻发音人")
    # print("\n注意：复刻发音人功能需要先训练模型")
    # print("请确保 clone_speaker_id 对应的模型已训练完成")
    # print("=" * 50)

    try:
        asyncio.run(demo_voice_clone_usage())
        print("\n✅ 所有演示完成！")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        print("请检查网络连接和API配置")
