#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用您的API凭证快速测试声音复刻功能

API信息：
- APP ID: 1181316113
- Access Token: XliULxhgl1UU0zUkMuuTjzcJkOyryWLA
- 声音ID: S_Faev7OGv1
"""

import asyncio
import os
from datetime import datetime
from voice_clone_tts_demo import VoiceCloneTTSDemo


async def test_your_voice_clone():
    """测试您的声音复刻功能"""
    
    # 您的API配置
    APP_ID = "1181316113"
    TOKEN = "XliULxhgl1UU0zUkMuuTjzcJkOyryWLA"
    CLONE_SPEAKER_ID = "S_Faev7OGv1"
    
    print("🎤 测试您的声音复刻功能")
    print("=" * 50)
    print(f"APP ID: {APP_ID}")
    print(f"声音ID: {CLONE_SPEAKER_ID}")
    print("=" * 50)
    
    # 创建演示实例
    demo = VoiceCloneTTSDemo(APP_ID, TOKEN)
    
    # 步骤1: 检查声音复刻模型状态
    print(f"\n📋 检查声音复刻模型状态...")
    try:
        status = demo.check_training_status(CLONE_SPEAKER_ID)
        if status and 'data' in status:
            model_status = status['data'].get('status', '未知')
            print(f"✅ 模型状态: {model_status}")
            
            if model_status == 'success':
                print("🎉 模型已训练完成，可以直接使用！")
                model_ready = True
            else:
                print(f"⚠️  模型状态: {model_status}")
                print("如果模型尚未训练完成，请等待或重新训练")
                model_ready = False
        else:
            print("❌ 无法获取模型状态")
            model_ready = False
            
    except Exception as e:
        print(f"❌ 查询状态时出错: {e}")
        print("尝试直接进行语音合成测试...")
        model_ready = True  # 尝试继续测试
    
    if not model_ready:
        print("\n💡 如果您需要训练新模型，请使用以下命令：")
        print(f"python voice_clone_tts_demo.py --mode train --audio-file your_audio.wav --speaker-id {CLONE_SPEAKER_ID}")
        return
    
    # 步骤2: 基础语音合成测试
    print(f"\n🎵 测试1: 基础语音合成")
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"test_basic_{timestamp}.wav"
        
        await demo.synthesize_with_clone(
            speaker_id=CLONE_SPEAKER_ID,
            text="您好，这是使用您的声音复刻模型生成的语音。听起来怎么样？",
            output_path=output_path,
            stream_audio=True
        )
        print(f"✅ 基础合成完成！文件保存为: {output_path}")
        
    except Exception as e:
        print(f"❌ 基础合成失败: {e}")
        return
    
    # 步骤3: 混音效果测试
    print(f"\n🎭 测试2: 混音效果")
    try:
        output_path = f"test_mix_{timestamp}.wav"
        
        await demo.synthesize_with_clone(
            speaker_id=CLONE_SPEAKER_ID,
            text="这是混音效果测试，将您的声音与预置发音人进行混合，创造独特的音色效果。",
            output_path=output_path,
            stream_audio=True,
            mix_with_preset=True,
            preset_speaker="zh_female_shuangkuaisisi_moon_bigtts",
            mix_factor=0.3
        )
        print(f"✅ 混音合成完成！文件保存为: {output_path}")
        
    except Exception as e:
        print(f"❌ 混音合成失败: {e}")
    
    # 步骤4: 流式合成测试
    print(f"\n🌊 测试3: 流式合成")
    try:
        async def test_text_stream():
            """测试文本流"""
            texts = [
                "这是流式语音合成的演示。",
                "文本会分段发送给服务器，",
                "音频也会实时生成和播放，",
                "提供更加流畅的用户体验。",
                "您觉得这个效果如何？"
            ]
            for text in texts:
                yield text
                await asyncio.sleep(0.8)  # 模拟文本生成间隔
        
        output_path = f"test_stream_{timestamp}.wav"
        
        await demo.synthesize_with_clone(
            speaker_id=CLONE_SPEAKER_ID,
            text_generator=test_text_stream,
            output_path=output_path,
            stream_audio=True
        )
        print(f"✅ 流式合成完成！文件保存为: {output_path}")
        
    except Exception as e:
        print(f"❌ 流式合成失败: {e}")
    
    # 步骤5: 长文本测试
    print(f"\n📖 测试4: 长文本合成")
    try:
        long_text = """
        人工智能技术正在快速发展，语音合成作为其中的重要分支，
        已经在多个领域得到了广泛应用。声音复刻技术能够模拟特定人的声音特征，
        为个性化语音服务提供了新的可能性。通过深度学习算法，
        我们可以训练出高质量的声音模型，生成自然流畅的语音。
        这项技术在教育、娱乐、辅助技术等领域都有着巨大的应用潜力。
        """
        
        output_path = f"test_long_{timestamp}.wav"
        
        await demo.synthesize_with_clone(
            speaker_id=CLONE_SPEAKER_ID,
            text=long_text.strip(),
            output_path=output_path,
            stream_audio=True
        )
        print(f"✅ 长文本合成完成！文件保存为: {output_path}")
        
    except Exception as e:
        print(f"❌ 长文本合成失败: {e}")
    
    print(f"\n🎉 所有测试完成！")
    print("生成的音频文件：")
    print(f"- test_basic_{timestamp}.wav (基础合成)")
    print(f"- test_mix_{timestamp}.wav (混音效果)")
    print(f"- test_stream_{timestamp}.wav (流式合成)")
    print(f"- test_long_{timestamp}.wav (长文本)")


async def interactive_test():
    """交互式测试"""
    
    APP_ID = "1181316113"
    TOKEN = "XliULxhgl1UU0zUkMuuTjzcJkOyryWLA"
    CLONE_SPEAKER_ID = "S_Faev7OGv1"
    
    demo = VoiceCloneTTSDemo(APP_ID, TOKEN)
    
    print("🎤 交互式声音复刻测试")
    print("=" * 40)
    print("请输入要合成的文本（输入 'quit' 退出）：")
    
    while True:
        try:
            text = input("\n> ").strip()
            
            if text.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not text:
                continue
            
            print("🎵 正在合成语音...")
            timestamp = datetime.now().strftime("%H%M%S")
            output_path = f"interactive_{timestamp}.wav"
            
            await demo.synthesize_with_clone(
                speaker_id=CLONE_SPEAKER_ID,
                text=text,
                output_path=output_path,
                stream_audio=True
            )
            
            print(f"✅ 合成完成！文件保存为: {output_path}")
            
        except KeyboardInterrupt:
            print("\n👋 程序被中断，再见！")
            break
        except Exception as e:
            print(f"❌ 合成失败: {e}")


def main():
    """主函数"""
    print("选择测试模式：")
    print("1. 自动测试所有功能")
    print("2. 交互式测试")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            asyncio.run(test_your_voice_clone())
        elif choice == "2":
            asyncio.run(interactive_test())
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")


if __name__ == "__main__":
    main()
