#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎TTS配置文件

在这里配置你的API密钥、发音人ID等参数
"""

# 火山引擎API配置
VOLCANO_CONFIG = {
    "app_id": "1181316113",  # 您的应用ID
    "token": "XliULxhgl1UU0zUkMuuTjzcJkOyryWLA",  # 您的访问令牌
    "secret_key": "Kvq3owedlPSfnDFs4FSjoj7wyfe3ADve",  # 您的密钥
    "host": "https://openspeech.bytedance.com"
}

# 预置发音人配置
PRESET_SPEAKERS = {
    # 中文发音人
    "zh_female_shuangkuaisisi": "zh_female_shuangkuaisisi_moon_bigtts",
    "zh_male_bvlazysheep": "zh_male_bvlazysheep",
    "zh_female_sweet": "zh_female_sweet",
    "zh_male_magnetic": "zh_male_magnetic",
    
    # 英文发音人
    "en_female_bella": "en_female_bella",
    "en_male_adam": "en_male_adam",
    
    # 多语言发音人
    "multilingual_female": "multilingual_female_1",
    "multilingual_male": "multilingual_male_1"
}

# 复刻发音人配置
CLONE_SPEAKERS = {
    # 您的复刻发音人
    "my_custom_voice": "S_Faev7OGv1",  # 您的复刻发音人ID
    "my_speaker_001": "S_Faev7OGv1",  # 备用配置
    "default_clone": "S_Faev7OGv1"    # 默认复刻发音人
}

# 混音配置预设
MIX_PRESETS = {
    "gentle_mix": {
        "speakers": [
            {"source_speaker": "zh_male_bvlazysheep", "mix_factor": 0.3}
        ]
    },
    "sweet_mix": {
        "speakers": [
            {"source_speaker": "zh_female_sweet", "mix_factor": 0.4}
        ]
    },
    "magnetic_mix": {
        "speakers": [
            {"source_speaker": "zh_male_magnetic", "mix_factor": 0.2}
        ]
    },
    "dual_mix": {
        "speakers": [
            {"source_speaker": "zh_male_bvlazysheep", "mix_factor": 0.2},
            {"source_speaker": "zh_female_sweet", "mix_factor": 0.1}
        ]
    }
}

# 音频参数配置
AUDIO_CONFIG = {
    "format": "pcm",  # 音频格式: pcm, mp3, wav
    "sample_rate": 24000,  # 采样率: 16000, 24000, 48000
    "post_process": {
        "pitch": 12,  # 音调调整: -12 到 12
        "speed": 1.0,  # 语速调整: 0.5 到 2.0
        "volume": 1.0  # 音量调整: 0.1 到 2.0
    }
}

# 训练配置
TRAINING_CONFIG = {
    "language": 0,  # 语言类型: 0=中文, 1=英文
    "model_type": 1,  # 模型类型: 1=标准模型
    "max_wait_time": 600,  # 最大等待时间(秒)
    "check_interval": 15  # 状态检查间隔(秒)
}

# 输出配置
OUTPUT_CONFIG = {
    "default_output_dir": "/Users/<USER>/Downloads",  # 默认输出目录
    "file_prefix": "tts_output",  # 文件名前缀
    "enable_realtime_play": True,  # 是否启用实时播放
    "save_to_file": True  # 是否保存到文件
}

# 获取配置的便捷函数
def get_volcano_config():
    """获取火山引擎API配置"""
    return VOLCANO_CONFIG

def get_preset_speaker(name):
    """获取预置发音人ID"""
    return PRESET_SPEAKERS.get(name)

def get_clone_speaker(name):
    """获取复刻发音人ID"""
    return CLONE_SPEAKERS.get(name)

def get_mix_preset(name):
    """获取混音预设"""
    return MIX_PRESETS.get(name)

def get_audio_config():
    """获取音频配置"""
    return AUDIO_CONFIG

def get_training_config():
    """获取训练配置"""
    return TRAINING_CONFIG

def get_output_config():
    """获取输出配置"""
    return OUTPUT_CONFIG

# 配置验证函数
def validate_config():
    """验证配置是否完整"""
    errors = []
    
    # 检查必要的API配置
    if not VOLCANO_CONFIG.get("app_id"):
        errors.append("缺少 app_id 配置")
    
    if not VOLCANO_CONFIG.get("token"):
        errors.append("缺少 token 配置")
    
    # 检查输出目录是否存在
    import os
    output_dir = OUTPUT_CONFIG.get("default_output_dir")
    if output_dir and not os.path.exists(output_dir):
        try:
            os.makedirs(output_dir, exist_ok=True)
        except Exception as e:
            errors.append(f"无法创建输出目录 {output_dir}: {e}")
    
    return errors

# 打印配置信息
def print_config_info():
    """打印当前配置信息"""
    print("🔧 当前配置信息:")
    print(f"  App ID: {VOLCANO_CONFIG['app_id']}")
    print(f"  Token: {VOLCANO_CONFIG['token'][:10]}...")
    print(f"  预置发音人数量: {len(PRESET_SPEAKERS)}")
    print(f"  复刻发音人数量: {len(CLONE_SPEAKERS)}")
    print(f"  混音预设数量: {len(MIX_PRESETS)}")
    print(f"  输出目录: {OUTPUT_CONFIG['default_output_dir']}")
    
    # 验证配置
    errors = validate_config()
    if errors:
        print("\n⚠️  配置问题:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n✅ 配置验证通过")

if __name__ == "__main__":
    print_config_info()
