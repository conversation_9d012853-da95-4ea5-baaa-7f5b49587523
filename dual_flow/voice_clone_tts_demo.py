#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎双流式TTS声音复刻示例

这个示例展示了如何使用火山引擎的双流式TTS API进行声音复刻，
包括音频上传、模型训练、状态监控和复刻语音合成。

功能特性：
1. 声音复刻模型训练和管理
2. 双流式TTS合成（支持复刻发音人）
3. 混音功能（复刻发音人与预置发音人混合）
4. 实时音频播放
5. 流式文本输入

使用方法：
python voice_clone_tts_demo.py --mode train --audio-file path/to/audio.wav --speaker-id your_speaker_id
python voice_clone_tts_demo.py --mode synthesize --speaker-id your_speaker_id --text "要合成的文本"
"""

import asyncio
import argparse
import sys
import os
from TTSWebSocketDemo import (
    run_demo, VoiceCloneManager, 
    example_text_generator, file_text_generator, interactive_text_generator
)


class VoiceCloneTTSDemo:
    """声音复刻TTS演示类"""
    
    def __init__(self, app_id: str, token: str):
        self.app_id = app_id
        self.token = token
        self.voice_clone_manager = VoiceCloneManager(app_id, token)
    
    async def train_voice_clone(self, audio_file: str, speaker_id: str, 
                               language: int = 0, model_type: int = 1):
        """训练声音复刻模型"""
        print(f"开始训练声音复刻模型...")
        print(f"音频文件: {audio_file}")
        print(f"发音人ID: {speaker_id}")
        
        if not os.path.exists(audio_file):
            raise FileNotFoundError(f"音频文件不存在: {audio_file}")
        
        try:
            # 上传音频并开始训练
            result = self.voice_clone_manager.upload_and_train(
                audio_file, speaker_id, language, model_type
            )
            
            print("训练请求已提交，等待训练完成...")
            
            # 等待训练完成
            success = self.voice_clone_manager.wait_for_training_completion(
                speaker_id, max_wait_time=600, check_interval=15
            )
            
            if success:
                print(f"✅ 声音复刻模型训练成功！发音人ID: {speaker_id}")
                return True
            else:
                print(f"❌ 声音复刻模型训练失败或超时")
                return False
                
        except Exception as e:
            print(f"❌ 训练过程中出错: {e}")
            return False
    
    def check_training_status(self, speaker_id: str):
        """检查训练状态"""
        try:
            status = self.voice_clone_manager.get_training_status(speaker_id)
            print(f"发音人 {speaker_id} 的训练状态:")
            print(f"状态详情: {status}")
            return status
        except Exception as e:
            print(f"❌ 查询状态失败: {e}")
            return None
    
    async def synthesize_with_clone(self, speaker_id: str, text: str = None, 
                                   output_path: str = "clone_output.wav",
                                   text_generator=None, stream_audio: bool = True,
                                   mix_with_preset: bool = False, 
                                   preset_speaker: str = "zh_male_bvlazysheep",
                                   mix_factor: float = 0.3):
        """使用复刻发音人进行语音合成"""
        
        print(f"开始使用复刻发音人进行语音合成...")
        print(f"复刻发音人ID: {speaker_id}")
        print(f"输出文件: {output_path}")
        
        # 配置混音参数
        mix_speakers = None
        if mix_with_preset:
            mix_speakers = [{
                "source_speaker": preset_speaker,
                "mix_factor": mix_factor
            }]
            print(f"启用混音功能，预置发音人: {preset_speaker}, 混音比例: {mix_factor}")
        
        # 配置后处理参数
        post_process = {"pitch": 12}  # 可以根据需要调整
        
        try:
            await run_demo(
                appId=self.app_id,
                token=self.token,
                speaker="",  # 使用复刻发音人时，预置speaker可以为空
                text=text or "",
                output_path=output_path,
                text_generator=text_generator,
                stream_audio=stream_audio,
                use_clone_speaker=True,
                clone_speaker_id=speaker_id,
                mix_speakers=mix_speakers,
                post_process=post_process
            )
            print(f"✅ 语音合成完成！输出文件: {output_path}")
            
        except Exception as e:
            print(f"❌ 语音合成失败: {e}")
            raise


def create_text_generators():
    """创建不同类型的文本生成器"""
    
    async def demo_text_generator():
        """演示文本生成器"""
        demo_texts = [
            "大家好，我是通过声音复刻技术生成的语音。",
            "这项技术可以模拟特定人的声音特征，",
            "让合成的语音听起来更加自然和个性化。",
            "希望这个演示能够展示声音复刻的强大功能。"
        ]
        for text in demo_texts:
            yield text
            await asyncio.sleep(0.5)
    
    return demo_text_generator


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='火山引擎声音复刻双流式TTS演示')
    
    # 基本参数
    parser.add_argument('--app-id', default='1181316113', help='火山引擎应用ID')
    parser.add_argument('--token', default='XliULxhgl1UU0zUkMuuTjzcJkOyryWLA', help='火山引擎访问令牌')
    
    # 模式选择
    parser.add_argument('--mode', choices=['train', 'synthesize', 'status'], required=False,
                       help='运行模式: train(训练), synthesize(合成), status(查询状态)')
    
    # 训练相关参数
    parser.add_argument('--audio-file', help='用于训练的音频文件路径')
    parser.add_argument('--speaker-id', required=True, help='发音人ID')
    parser.add_argument('--language', type=int, default=0, help='语言类型 (0: 中文)')
    parser.add_argument('--model-type', type=int, default=1, help='模型类型')
    
    # 合成相关参数
    parser.add_argument('--text', help='要合成的文本')
    parser.add_argument('--output', default='clone_output.wav', help='输出音频文件路径')
    parser.add_argument('--input-file', help='从文件读取文本')
    parser.add_argument('--stream', action='store_true', help='启用流式输入')
    parser.add_argument('--play', action='store_true', default=True, help='实时播放音频')
    parser.add_argument('--interactive', action='store_true', help='交互式输入文本')
    
    # 混音参数
    parser.add_argument('--mix', action='store_true', help='启用与预置发音人混音')
    parser.add_argument('--preset-speaker', default='zh_male_bvlazysheep', help='预置发音人ID')
    parser.add_argument('--mix-factor', type=float, default=0.3, help='混音比例 (0.0-1.0)')
    
    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_arguments()
    
    # 创建演示实例
    demo = VoiceCloneTTSDemo(args.app_id, args.token)
    
    if args.mode == 'train':
        # 训练模式
        if not args.audio_file:
            print("❌ 训练模式需要指定 --audio-file 参数")
            sys.exit(1)
        
        success = await demo.train_voice_clone(
            args.audio_file, args.speaker_id, args.language, args.model_type
        )
        
        if not success:
            sys.exit(1)
    
    elif args.mode == 'status':
        # 状态查询模式
        demo.check_training_status(args.speaker_id)
    
    elif args.mode == 'synthesize':
        # 合成模式
        text_generator = None
        
        if args.interactive:
            text_generator = interactive_text_generator
        elif args.input_file:
            text_generator = lambda: file_text_generator(args.input_file)
        elif args.stream:
            text_generator = create_text_generators()
        
        await demo.synthesize_with_clone(
            speaker_id=args.speaker_id,
            text=args.text,
            output_path=args.output,
            text_generator=text_generator,
            stream_audio=args.play,
            mix_with_preset=args.mix,
            preset_speaker=args.preset_speaker,
            mix_factor=args.mix_factor
        )


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        sys.exit(1)
