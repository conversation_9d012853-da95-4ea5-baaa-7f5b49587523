#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
火山引擎声音复刻简单示例

这是一个简化的示例，展示如何快速使用声音复刻功能。
"""

import asyncio
import os
from voice_clone_tts_demo import VoiceCloneTTSDemo


async def simple_voice_clone_demo():
    """简单的声音复刻演示"""
    
    # 配置参数
    APP_ID = "1181316113"
    TOKEN = "XliULxhgl1UU0zUkMuuTjzcJkOyryWLA"
    SPEAKER_ID = "S_Faev7OGv1"  # 您的复刻发音人ID
    
    # 创建演示实例
    demo = VoiceCloneTTSDemo(APP_ID, TOKEN)
    
    print("🎤 火山引擎声音复刻演示")
    print("=" * 50)
    
    # 步骤1: 检查是否已有训练好的模型
    print(f"📋 检查发音人 {SPEAKER_ID} 的状态...")
    status = demo.check_training_status(SPEAKER_ID)
    
    model_ready = False
    if status and 'data' in status:
        if status['data'].get('status') == 'success':
            print(f"✅ 发音人 {SPEAKER_ID} 已训练完成，可以直接使用")
            model_ready = True
        else:
            print(f"⚠️  发音人 {SPEAKER_ID} 状态: {status['data'].get('status', '未知')}")
    
    # 步骤2: 如果模型未就绪，提示用户训练
    if not model_ready:
        print(f"\n🔧 发音人 {SPEAKER_ID} 尚未训练完成")
        print("请先使用以下命令训练模型：")
        print(f"python voice_clone_tts_demo.py --mode train --audio-file your_audio.wav --speaker-id {SPEAKER_ID}")
        print("\n或者修改 SPEAKER_ID 为已训练完成的发音人ID")
        return
    
    # 步骤3: 使用复刻发音人进行语音合成
    print(f"\n🎵 开始使用复刻发音人 {SPEAKER_ID} 进行语音合成...")
    
    # 示例1: 基础文本合成
    print("\n📝 示例1: 基础文本合成")
    await demo.synthesize_with_clone(
        speaker_id=SPEAKER_ID,
        text="大家好，我是通过声音复刻技术生成的语音。这项技术可以模拟特定人的声音特征。",
        output_path="example1_basic.wav",
        stream_audio=True
    )
    print("✅ 基础合成完成，文件保存为: example1_basic.wav")
    
    # 示例2: 混音合成
    print("\n🎭 示例2: 混音合成（复刻发音人 + 预置发音人）")
    await demo.synthesize_with_clone(
        speaker_id=SPEAKER_ID,
        text="这是混音效果演示，将复刻发音人与预置发音人进行混合，创造独特的音色效果。",
        output_path="example2_mix.wav",
        stream_audio=True,
        mix_with_preset=True,
        preset_speaker="zh_female_shuangkuaisisi_moon_bigtts",
        mix_factor=0.3
    )
    print("✅ 混音合成完成，文件保存为: example2_mix.wav")
    
    # 示例3: 流式文本合成
    print("\n🌊 示例3: 流式文本合成")
    
    async def demo_text_stream():
        """演示文本流"""
        texts = [
            "这是流式文本合成的演示。",
            "文本会分段发送给服务器，",
            "音频也会实时生成和播放，",
            "提供更加流畅的用户体验。"
        ]
        for text in texts:
            yield text
            await asyncio.sleep(0.8)  # 模拟文本生成间隔
    
    await demo.synthesize_with_clone(
        speaker_id=SPEAKER_ID,
        text_generator=demo_text_stream,
        output_path="example3_stream.wav",
        stream_audio=True
    )
    print("✅ 流式合成完成，文件保存为: example3_stream.wav")
    
    print("\n🎉 所有演示完成！")
    print("生成的音频文件：")
    print("- example1_basic.wav (基础合成)")
    print("- example2_mix.wav (混音合成)")
    print("- example3_stream.wav (流式合成)")


async def train_new_voice_model():
    """训练新的声音模型示例"""

    APP_ID = "1181316113"
    TOKEN = "XliULxhgl1UU0zUkMuuTjzcJkOyryWLA"
    
    # 配置训练参数
    AUDIO_FILE = "training_audio.wav"  # 替换为你的音频文件路径
    SPEAKER_ID = "my_new_speaker_002"  # 新的发音人ID
    
    if not os.path.exists(AUDIO_FILE):
        print(f"❌ 音频文件不存在: {AUDIO_FILE}")
        print("请准备一个音频文件用于训练，建议：")
        print("- 时长: 10-60秒")
        print("- 格式: WAV, MP3等")
        print("- 质量: 清晰、无噪音")
        print("- 内容: 单人发音，包含丰富语音特征")
        return
    
    demo = VoiceCloneTTSDemo(APP_ID, TOKEN)
    
    print(f"🔧 开始训练声音复刻模型...")
    print(f"音频文件: {AUDIO_FILE}")
    print(f"发音人ID: {SPEAKER_ID}")
    
    success = await demo.train_voice_clone(
        audio_file=AUDIO_FILE,
        speaker_id=SPEAKER_ID
    )
    
    if success:
        print(f"✅ 训练成功！现在可以使用发音人ID: {SPEAKER_ID}")
        
        # 训练完成后立即测试
        print("\n🎵 测试新训练的发音人...")
        await demo.synthesize_with_clone(
            speaker_id=SPEAKER_ID,
            text="恭喜！您的声音复刻模型训练成功，这是使用新模型生成的第一段语音。",
            output_path=f"{SPEAKER_ID}_test.wav",
            stream_audio=True
        )
        print(f"✅ 测试完成，文件保存为: {SPEAKER_ID}_test.wav")
    else:
        print("❌ 训练失败，请检查音频文件和网络连接")


def main():
    """主函数"""
    print("选择运行模式：")
    print("1. 演示声音复刻合成功能（需要已训练的模型）")
    print("2. 训练新的声音复刻模型")
    
    try:
        choice = input("请输入选择 (1 或 2): ").strip()
        
        if choice == "1":
            asyncio.run(simple_voice_clone_demo())
        elif choice == "2":
            asyncio.run(train_new_voice_model())
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")


if __name__ == "__main__":
    main()
